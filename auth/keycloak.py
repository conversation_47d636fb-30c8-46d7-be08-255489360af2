"""
Keycloak authentication implementation
"""

import requests
import logging
from datetime import datetime, timedelta
from .base import TokenManager
from utils.exceptions import AuthenticationError

logger = logging.getLogger(__name__)


class KeycloakTokenManager(TokenManager):
    """Keycloak token manager using Client Credentials flow for service-to-service authentication"""

    def __init__(self, auth_url: str, client_id: str, client_secret: str):
        super().__init__()
        self.auth_url = auth_url
        self.client_id = client_id
        self.client_secret = client_secret
        
        # Session for connection pooling
        self.session = requests.Session()

    def _refresh_token(self) -> None:
        """Get the access token using Client Credentials flow"""
        
        logger.debug("Getting access token using Client Credentials flow...")
        
        payload = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret
        }

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        }

        try:
            response = self.session.post(
                self.auth_url,
                data=payload,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                self._handle_token_response(response.json())
            else:
                error_data = response.json() if response.content else {}
                error_description = error_data.get('error_description', 'Authentication failed')
                raise AuthenticationError(f"Client credentials authentication failed: {error_description}")
                
        except requests.RequestException as e:
            logger.error(f"Network error during authentication: {e}")
            raise AuthenticationError(f"Network error during authentication: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during authentication: {e}")
            raise AuthenticationError(f"Authentication failed: {e}")
    
    
    def _handle_token_response(self, token_data: dict) -> None:
        """Handle token response from Keycloak"""
        # Extract access token
        self.access_token = token_data.get('access_token')
        if not self.access_token:
            raise AuthenticationError("No access token in response")

        # Calculate expiry time (Client Credentials doesn't provide refresh tokens)
        expires_in = token_data.get('expires_in', 3600)
        self.expires_at = datetime.now() + timedelta(seconds=expires_in)

        logger.debug("Client credentials authentication successful")

    def update_refresh_token(self, new_refresh_token: str) -> None:
        """Update the refresh token (useful for token rotation)"""
        self.refresh_token = new_refresh_token
        logger.debug("Refresh token updated")

    
    def get_token_info(self) -> dict:
        """Get information about the current token"""
        return {
            "has_access_token": self.access_token is not None,
            "authentication_type": "client_credentials",
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "is_expired": self._is_token_expired(),
            "time_until_expiry": str(self.expires_at - datetime.now()) if self.expires_at else None
        }
