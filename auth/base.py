"""
Base authentication classes
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Optional


class TokenManager(ABC):
    """Abstract base class for token management"""

    def __init__(self):
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.expires_at: Optional[datetime] = None
        self.refresh_buffer: int = 300  # 5-minutes buffer before expiry

    def get_valid_token(self) -> str:
        """Get a valid access token, refreshing if necessary"""
        if self.access_token is None or self._is_token_expired():
            self._refresh_token()
        
        if not self.access_token:
            raise Exception("Failed to obtain valid access token")
            
        return self.access_token

    def _is_token_expired(self) -> bool:
        """Check if the token is expired or about to expire"""
        if self.expires_at is None:
            return True
        
        # Check if the token expires within the buffer time
        buffer_time = datetime.now() + timedelta(seconds=self.refresh_buffer)
        return self.expires_at <= buffer_time

    @abstractmethod
    def _refresh_token(self) -> None:
        """Refresh the bearer token - must be implemented by subclasses"""
        pass

    def invalidate_token(self) -> None:
        """Invalidate the current token to force refresh"""
        self.access_token = None
        self.expires_at = None

