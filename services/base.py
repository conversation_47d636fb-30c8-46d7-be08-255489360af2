"""
Base classes and enums for reward services
"""

from enum import Enum
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class RewardType(str, Enum):
    """Types of rewards supported"""
    FREE_SPINS = "free_spins"
    BONUS = "bonus"
    CASHBACK = "cashback"


class BaseRewardConfig(BaseModel):
    """Base configuration for reward awards"""
    currency_code: str = Field(default="USD", description="Currency code for the reward")
    total_value: float = Field(gt=0, description="Total value of the reward")
    assigned_from: Optional[datetime] = Field(None, description="When the reward can be assigned from")
    assigned_until: Optional[datetime] = Field(None, description="When the reward can be assigned until")
    usage_conditions: Optional[List[Dict[str, Any]]] = Field(None, description="Usage conditions for the reward")
    expiry_hours: Optional[int] = Field(None, description="Hours until reward expires")


class BaseRewardTemplate(BaseModel):
    """Base template for rewards"""
    key: str = Field(description="Unique template key")
    name: str = Field(description="Human-readable template name")
    is_enabled: bool = Field(default=True, description="Whether the template is enabled")
    tenant_integration_id: Optional[str] = Field(None, description="Tenant integration ID")
    template_id: Optional[str] = Field(None, description="Template ID from API")
    created_at: Optional[datetime] = Field(None, description="When the template was created")
    updated_at: Optional[datetime] = Field(None, description="When the template was last updated")
    config: Optional[BaseRewardConfig] = Field(None, description="Reward configuration")


class BaseRewardAssignment(BaseModel):
    """Base assignment for rewards"""
    assignment_id: str = Field(description="Unique assignment ID")
    template_key: str = Field(description="Template key this assignment is based on")
    owner_id: str = Field(description="Owner ID (usually player ID)")
    owner_type: str = Field(default="player", description="Type of owner")
    reward_type: RewardType = Field(description="Type of reward")
    status: str = Field(description="Assignment status")
    total_value: float = Field(description="Total value of the assignment")
    currency_code: str = Field(default="USD", description="Currency code")
    created_at: datetime = Field(description="When the assignment was created")
    expires_at: Optional[datetime] = Field(None, description="When the assignment expires")
    amount_used: float = Field(default=0.0, description="Amount already used from the reward")
    amount_converted: float = Field(default=0.0, description="Amount converted to real money")