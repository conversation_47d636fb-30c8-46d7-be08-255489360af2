"""
Freespin service - consolidated business logic for freespins
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from clients.elantil.client import ElantilClient
from utils.exceptions import FreeSpinError, TemplateError, APIError
from .models import FreeSpinTemplate, FreeSpinConfig, FreeSpinAssignment, FreeSpinType
from .constants import ElantilEndpoints

logger = logging.getLogger(__name__)


class FreeSpinService:
    """Service for managing freespin templates and assignments"""
    
    def __init__(self, elantil_client: ElantilClient, tenant_integration_id: Optional[str] = None):
        self.elantil_client = elantil_client
        self.tenant_integration_id = tenant_integration_id
        self._template_cache: Dict[str, FreeSpinTemplate] = {}
    
    # Template Management Methods
    
    def create_template(self, config: FreeSpinConfig, key: str = None, name: str = None) -> FreeSpinTemplate:
        """Create a new free spin template"""
        
        # Generate key and name if not provided
        key = key or self._generate_template_key(config)
        name = name or self._generate_template_name(config)
        
        # Create template object
        template = FreeSpinTemplate(
            key=key,
            name=name,
            config=config,
            tenant_integration_id=self.tenant_integration_id,
            created_at=datetime.now()
        )
        
        logger.info(f"Creating template: {key}")
        
        try:
            # Convert to Elantil format and create via API
            template_data = template.to_elantil_format()
            response = self.elantil_client.post(ElantilEndpoints.PROMOTION_TEMPLATES, template_data)
            
            # Update template with response data
            if "data" in response:
                template.template_id = response["data"].get("id")
            
            # Cache the template
            self._template_cache[key] = template
            
            logger.info(f"Template created successfully: {key}")
            return template
            
        except APIError as e:
            logger.error(f"Failed to create template {key}: {e}")
            raise TemplateError(f"Failed to create template: {e}")
    
    def create_game_specific_template(self, game_id: str, spin_count: int, total_value: float, **kwargs) -> FreeSpinTemplate:
        """Create a game-specific free spin template"""
        config = FreeSpinConfig(
            spin_type=FreeSpinType.GAME_SPECIFIC,
            spin_count=spin_count,
            total_value=total_value,
            game_id=game_id,
            **kwargs
        )
        return self.create_template(config)
    
    def create_provider_specific_template(self, provider_id: str, spin_count: int, total_value: float, **kwargs) -> FreeSpinTemplate:
        """Create a provider-specific free spin template"""
        config = FreeSpinConfig(
            spin_type=FreeSpinType.PROVIDER_SPECIFIC,
            spin_count=spin_count,
            total_value=total_value,
            provider_id=provider_id,
            **kwargs
        )
        return self.create_template(config)
    
    def create_arbitrary_template(self, spin_count: int, total_value: float, **kwargs) -> FreeSpinTemplate:
        """Create an arbitrary free spin template"""
        config = FreeSpinConfig(
            spin_type=FreeSpinType.ARBITRARY,
            spin_count=spin_count,
            total_value=total_value,
            **kwargs
        )
        return self.create_template(config)
    
    def list_templates(self, limit: int = 100, offset: int = 0) -> List[FreeSpinTemplate]:
        """List free spin templates"""
        try:
            params = {"limit": limit, "offset": offset}
            response = self.elantil_client.get(ElantilEndpoints.PROMOTION_TEMPLATES, params)
            
            templates = []
            if "data" in response:
                for template_data in response["data"]:
                    template = FreeSpinTemplate.from_elantil_response({"data": template_data})
                    templates.append(template)
                    # Cache template
                    self._template_cache[template.key] = template
            
            return templates
            
        except APIError as e:
            logger.error(f"Failed to list templates: {e}")
            raise TemplateError(f"Failed to list templates: {e}")
    
    def get_template(self, template_key: str) -> Optional[FreeSpinTemplate]:
        """Get a specific template by key"""
        
        # Check cache first
        if template_key in self._template_cache:
            return self._template_cache[template_key]
        
        try:
            endpoint = ElantilEndpoints.PROMOTION_TEMPLATE_BY_KEY.format(key=template_key)
            response = self.elantil_client.get(endpoint)
            
            if "data" in response:
                template = FreeSpinTemplate.from_elantil_response(response)
                self._template_cache[template_key] = template
                return template
            
            return None
            
        except APIError as e:
            if e.status_code == 404:
                return None
            logger.error(f"Failed to get template {template_key}: {e}")
            raise TemplateError(f"Failed to get template: {e}")
    
    # Assignment Management Methods
    
    def assign_free_spins(self, template_key: str, owner_id: str, owner_type: str = "player") -> FreeSpinAssignment:
        """Assign free spins to a player"""
        
        logger.info(f"Assigning free spins: template={template_key}, owner={owner_id}")
        
        try:
            # Prepare assignment data
            assignment_data = {
                "data": {
                    "type": "promotion-assignments",
                    "attributes": {
                        "ownerId": owner_id,
                        "ownerType": owner_type
                    }
                }
            }
            
            # Make API call
            endpoint = ElantilEndpoints.PROMOTION_TEMPLATE_ASSIGN.format(template_key=template_key)
            response = self.elantil_client.post(endpoint, assignment_data)
            
            # Create the assignment object from response
            assignment = FreeSpinAssignment.from_elantil_response(response)
            
            logger.info(f"Free spins assigned successfully: {assignment.assignment_id}")
            return assignment
            
        except APIError as e:
            logger.error(f"Failed to assign free spins: {e}")
            raise FreeSpinError(f"Failed to assign free spins: {e}")
    
    def get_assignment(self, assignment_id: str) -> Optional[FreeSpinAssignment]:
        """Get free spin assignment details"""
        
        try:
            endpoint = ElantilEndpoints.OWNER_PROMOTION_BY_ID.format(assignment_id=assignment_id)
            response = self.elantil_client.get(endpoint)
            
            if "data" in response:
                return FreeSpinAssignment.from_elantil_response(response)
            
            return None
            
        except APIError as e:
            if e.status_code == 404:
                return None
            logger.error(f"Failed to get assignment {assignment_id}: {e}")
            raise FreeSpinError(f"Failed to get assignment: {e}")
    
    def cancel_assignment(self, assignment_id: str) -> bool:
        """Cancel a free spin assignment"""
        
        logger.info(f"Cancelling assignment: {assignment_id}")
        
        try:
            endpoint = ElantilEndpoints.OWNER_PROMOTION_CANCEL.format(assignment_id=assignment_id)
            self.elantil_client.post(endpoint, {})
            
            logger.info(f"Assignment cancelled successfully: {assignment_id}")
            return True
            
        except APIError as e:
            logger.error(f"Failed to cancel assignment {assignment_id}: {e}")
            raise FreeSpinError(f"Failed to cancel assignment: {e}")
    
    def forfeit_assignment(self, assignment_id: str) -> bool:
        """Forfeit a free spin assignment"""
        
        logger.info(f"Forfeiting assignment: {assignment_id}")
        
        try:
            endpoint = ElantilEndpoints.OWNER_PROMOTION_FORFEIT.format(assignment_id=assignment_id)
            self.elantil_client.post(endpoint, {})
            
            logger.info(f"Assignment forfeited successfully: {assignment_id}")
            return True
            
        except APIError as e:
            logger.error(f"Failed to forfeit assignment {assignment_id}: {e}")
            raise FreeSpinError(f"Failed to forfeit assignment: {e}")
    
    def bulk_assign_free_spins(self, template_key: str, owner_ids: List[str], owner_type: str = "player") -> List[FreeSpinAssignment]:
        """Assign free spins to multiple players"""
        
        logger.info(f"Bulk assigning free spins: template={template_key}, count={len(owner_ids)}")
        
        assignments = []
        failed_assignments = []
        
        for owner_id in owner_ids:
            try:
                assignment = self.assign_free_spins(template_key, owner_id, owner_type)
                assignments.append(assignment)
            except FreeSpinError as e:
                logger.warning(f"Failed to assign to {owner_id}: {e}")
                failed_assignments.append({"owner_id": owner_id, "error": str(e)})
        
        logger.info(f"Bulk assignment completed: {len(assignments)} successful, {len(failed_assignments)} failed")
        
        if failed_assignments:
            logger.warning(f"Failed assignments: {failed_assignments}")
        
        return assignments
    
    def get_player_assignments(self, player_id: str, active_only: bool = True) -> List[FreeSpinAssignment]:
        """Get all assignments for a player"""
        
        try:
            params = {"ownerId": player_id}
            if active_only:
                params["status"] = "active"
            
            response = self.elantil_client.get(ElantilEndpoints.OWNER_PROMOTIONS, params)
            
            assignments = []
            if "data" in response:
                for assignment_data in response["data"]:
                    assignment = FreeSpinAssignment.from_elantil_response({"data": assignment_data})
                    assignments.append(assignment)
            
            return assignments
            
        except APIError as e:
            logger.error(f"Failed to get player assignments for {player_id}: {e}")
            raise FreeSpinError(f"Failed to get player assignments: {e}")
    
    # Utility Methods
    
    def _generate_template_key(self, config: FreeSpinConfig) -> str:
        """Generate a unique template key"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if config.spin_type == FreeSpinType.GAME_SPECIFIC:
            return f"game_{config.game_id}_{config.spin_count}_{config.total_value}_{timestamp}"
        elif config.spin_type == FreeSpinType.PROVIDER_SPECIFIC:
            return f"provider_{config.provider_id}_{config.spin_count}_{config.total_value}_{timestamp}"
        elif config.spin_type == FreeSpinType.MULTI_GAME:
            return f"multi_game_{config.spin_count}_{config.total_value}_{timestamp}"
        else:
            return f"arbitrary_{config.spin_count}_{config.total_value}_{timestamp}"
    
    def _generate_template_name(self, config: FreeSpinConfig) -> str:
        """Generate a human-readable template name"""
        base_name = f"{config.spin_count} Free Spins (${config.total_value})"
        
        if config.spin_type == FreeSpinType.GAME_SPECIFIC:
            return f"{base_name} - {config.game_id}"
        elif config.spin_type == FreeSpinType.PROVIDER_SPECIFIC:
            return f"{base_name} - {config.provider_id} Games"
        elif config.spin_type == FreeSpinType.MULTI_GAME:
            return f"{base_name} - Multi-Game Choice"
        else:
            return f"{base_name} - Arbitrary"
    
    def get_assignment_statistics(self) -> Dict[str, Any]:
        """Get assignment statistics"""
        # This would typically make API calls to get statistics
        # Implementation depends on the specific API endpoints available
        return {
            "total_assignments": 0,
            "active_assignments": 0,
            "completed_assignments": 0,
            "total_value_assigned": 0.0,
            "total_value_used": 0.0
        }