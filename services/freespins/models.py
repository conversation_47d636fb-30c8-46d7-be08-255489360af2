"""
Free spin types and data models
"""

from enum import Enum
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from pydantic import Field, field_validator, ValidationInfo
from ..base import BaseRewardConfig, BaseRewardTemplate, BaseRewardAssignment, RewardType


class FreeSpinType(str, Enum):
    """Types of free spins supported"""
    ARBITRARY = "arbitrary"
    GAME_SPECIFIC = "game_specific"
    MULTI_GAME = "multi_game"
    PROVIDER_SPECIFIC = "provider_specific"


class FreeSpinConfig(BaseRewardConfig):
    """Configuration for free spin awards"""
    reward_type: RewardType = Field(default=RewardType.FREE_SPINS, description="Reward type")
    spin_type: FreeSpinType = Field(description="Type of free spin")
    spin_count: int = Field(gt=0, description="Number of free spins")
    
    # Optional fields based on the spin type
    game_id: Optional[str] = Field(None, description="Specific game ID for game-specific spins")
    game_list: Optional[List[str]] = Field(None, description="List of games for multi-game choice")
    provider_id: Optional[str] = Field(None, description="Provider ID for provider-specific spins")
    
    # Additional configuration
    wagering_requirement: Optional[float] = Field(None, description="Amount required to wage free spins")
    max_conversion_amount: Optional[float] = Field(None, description="Maximum conversion amount")
    
    @field_validator('assigned_until')
    def validate_assigned_until(cls, v, info: ValidationInfo):
        """Ensure assigned_until is after assigned_from"""
        if v and info.data.get('assigned_from') and v <= info.data.get('assigned_from'):
            raise ValueError("assigned_until must be after assigned_from")
        return v
    
    @field_validator('game_list')
    def validate_game_list(cls, v, info: ValidationInfo):
        """Validate game_list for the multi-game type"""
        spin_type = info.data.get('spin_type')
        if spin_type == FreeSpinType.MULTI_GAME and not v:
            raise ValueError("game_list is required for multi_game spin type")
        return v
    
    @field_validator('game_id')
    def validate_game_id(cls, v, info: ValidationInfo):
        """Validate game_id for the game-specific type"""
        spin_type = info.data.get('spin_type')
        if spin_type == FreeSpinType.GAME_SPECIFIC and not v:
            raise ValueError("game_id is required for game_specific spin type")
        return v
    
    @field_validator('provider_id')
    def validate_provider_id(cls, v, info: ValidationInfo):
        """Validate provider_id for the provider-specific type"""
        spin_type = info.data.get('spin_type')
        if spin_type == FreeSpinType.PROVIDER_SPECIFIC and not v:
            raise ValueError("provider_id is required for provider_specific spin type")
        return v


class FreeSpinTemplate(BaseRewardTemplate):
    """Free spin template model"""
    reward_type: RewardType = Field(default=RewardType.FREE_SPINS, description="Reward type")
    config: FreeSpinConfig = Field(description="Free spin configuration")

    def to_elantil_format(self) -> Dict[str, Any]:
        """Convert to Elantil API format"""
        # Calculate assigned_from and assigned_until if not set
        now = datetime.now()
        assigned_from = self.config.assigned_from or now
        assigned_until = self.config.assigned_until or (now + timedelta(days=30))

        # Base template data
        template_data: Dict[str, Any] = {
            "type": "promotion-templates",
            "attributes": {
                "key": self.key,
                "name": self.name,
                "type": "FreeSpins",
                "isEnabled": self.is_enabled,
                "currencyCode": self.config.currency_code,
                "count": self.config.spin_count,
                "amount": str(self.config.total_value),
                "assignedFrom": assigned_from.isoformat() + "Z",
                "assignedUntil": assigned_until.isoformat() + "Z",
                "usageConditions": self.config.usage_conditions,
            }
        }

        # Add max conversion amount if specified (only if it has a value)
        if self.config.max_conversion_amount and self.config.max_conversion_amount > 0:
            template_data["attributes"]["maxConversionAmount"] = str(self.config.max_conversion_amount)

        # Add the wagering requirement if specified (only if it has a value)
        if self.config.wagering_requirement and self.config.wagering_requirement > 0:
            template_data["attributes"]["wageringRequirementAmount"] = str(self.config.wagering_requirement)

        # Add game/provider specific configuration
        config_data: Dict[str, Any] = {}

        if self.config.spin_type == FreeSpinType.GAME_SPECIFIC and self.config.game_id:
            config_data.update({
                "tenantIntegrationId": self.tenant_integration_id,
                "providerId": "hub88",
                "providerNamespace": "integrations",
                "id": "11f4088b-47c6-4820-865f-c7d1d3e9728d",
                "externalId": self.config.game_id
            })
        elif self.config.spin_type == FreeSpinType.PROVIDER_SPECIFIC and self.config.provider_id:
            config_data.update({
                "tenantIntegrationId": self.tenant_integration_id,
                "providerId": self.config.provider_id,
                "providerNamespace": "integrations",
                "id": "11f4088b-47c6-4820-865f-c7d1d3e9728d"
            })
        elif self.config.spin_type == FreeSpinType.MULTI_GAME and self.config.game_list:
            config_data.update({
                "tenantIntegrationId": self.tenant_integration_id,
                "gameOptions": self.config.game_list,
                "providerId": "hub88",
                "providerNamespace": "integrations",
                "id": "11f4088b-47c6-4820-865f-c7d1d3e9728d"
            })

        # Only add config if we have data
        if config_data:
            template_data["attributes"]["config"] = config_data  # type: ignore

        return {"data": template_data}


class FreeSpinAssignment(BaseRewardAssignment):
    """Free spin assignment model"""
    reward_type: RewardType = Field(default=RewardType.FREE_SPINS, description="Reward type")
    spins_remaining: int = Field(description="Number of spins remaining")
    spins_used: int = Field(default=0, description="Number of spins used")
    
    @classmethod
    def from_elantil_response(cls, response_data: Dict[str, Any]) -> "FreeSpinAssignment":
        """Create assignment from Elantil API response"""
        data = response_data.get("data", {})
        attributes = data.get("attributes", {})
        
        return cls(
            assignment_id=data.get("id", ""),
            template_key=attributes.get("templateKey", ""),
            owner_id=attributes.get("ownerId", ""),
            owner_type=attributes.get("ownerType", "player"),
            reward_type=RewardType.FREE_SPINS,
            status=attributes.get("status", "unknown"),
            total_value=float(attributes.get("totalValue", 0)),
            currency_code=attributes.get("currencyCode", "USD"),
            created_at=datetime.fromisoformat(attributes.get("createdAt", "").replace("Z", "")),
            expires_at=datetime.fromisoformat(attributes.get("expiresAt", "").replace("Z", "")) if attributes.get("expiresAt") else None,
            amount_used=float(attributes.get("amountConverted", 0)),
            spins_remaining=attributes.get("spinsRemaining", 0),
            spins_used=attributes.get("spinsUsed", 0)
        )
