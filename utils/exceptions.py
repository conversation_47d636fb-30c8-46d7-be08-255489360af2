"""
Custom exceptions for Elantil platform
"""


class ElantilError(Exception):
    """Base exception for all Elantil platform errors"""
    pass


class AuthenticationError(ElantilError):
    """Raised when authentication fails"""
    pass


class APIError(ElantilError):
    """Raised when API requests fail"""
    
    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data or {}


class ConfigurationError(ElantilError):
    """Raised when configuration is invalid"""
    pass


class FreeSpinError(ElantilError):
    """Raised when free spin operations fail"""
    pass


class TemplateError(FreeSpinError):
    """Raised when template operations fail"""
    pass

