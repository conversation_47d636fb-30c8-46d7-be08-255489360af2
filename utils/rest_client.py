"""
Generic REST client with authentication support
"""

import requests
import logging
from typing import Dict, Any
from auth.base import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .exceptions import APIError, AuthenticationError

logger = logging.getLogger(__name__)


class APIClient:
    """Generic API client with token management and error handling"""

    def __init__(self, token_manager: TokenManager, base_url: str = "", tenant_id: str = None):
        self.token_manager = token_manager
        self.base_url = base_url.rstrip('/')
        self.tenant_id = tenant_id
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/vnd.api+json',
            'Accept': 'application/vnd.api+json'
        })
        
        if tenant_id:
            self.session.headers.update({'x-tenant-id': tenant_id})

    def _get_headers(self, additional_headers: Dict[str, str] = None) -> Dict[str, str]:
        """Get headers with the current bearer token"""
        try:
            token = self.token_manager.get_valid_token()
            headers = {
                'Authorization': f'Bearer {token}',
            }
            
            if additional_headers:
                headers.update(additional_headers)
                
            return headers
        except Exception as e:
            raise AuthenticationError(f"Failed to get valid token: {e}")

    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make the authenticated request with error handling"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}" if self.base_url else endpoint
        
        # Get authentication headers and merge with session headers
        auth_headers = self._get_headers(kwargs.pop('headers', None))
        
        # Merge session headers with auth headers (auth headers take precedence)
        merged_headers = dict(self.session.headers)
        merged_headers.update(auth_headers)
        kwargs['headers'] = merged_headers
        
        # Set timeout if not provided
        kwargs.setdefault('timeout', 30)
        
        logger.debug(f"Making {method} request to {url}")
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            # Handle authentication errors
            if response.status_code == 401:
                logger.warning("Received 401, attempting token refresh")
                try:
                    # Force token refresh
                    self.token_manager.get_valid_token()
                    auth_headers = self._get_headers(kwargs.get('headers'))
                    kwargs['headers'] = auth_headers
                    response = self.session.request(method, url, **kwargs)
                except Exception as e:
                    raise AuthenticationError(f"Token refresh failed: {e}")
            
            # Log response details
            logger.debug(f"Response: {response.status_code} - {response.reason}")
            
            return response
            
        except requests.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise APIError(f"Request failed: {e}")

    @staticmethod
    def _handle_response(response: requests.Response) -> Dict[str, Any]:
        """Handle API response and extract data"""
        try:
            if response.status_code >= 400:
                error_data = {}
                try:
                    error_data = response.json()
                except:
                    pass
                
                error_msg = f"API request failed: {response.status_code} - {response.reason}"
                if error_data:
                    error_msg += f" - {error_data}"
                
                raise APIError(error_msg, response.status_code, error_data)
            
            # Return JSON data if available
            if response.content:
                return response.json()
            else:
                return {}
                
        except APIError:
            raise
        except Exception as e:
            raise APIError(f"Failed to parse response: {e}")

    def get(self, endpoint: str, params: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """GET request"""
        if params:
            kwargs['params'] = params
        response = self._make_request('GET', endpoint, **kwargs)
        return self._handle_response(response)

    def post(self, endpoint: str, data: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """POST request"""
        if data:
            kwargs['json'] = data
        response = self._make_request('POST', endpoint, **kwargs)
        return self._handle_response(response)

    def put(self, endpoint: str, data: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """PUT request"""
        if data:
            kwargs['json'] = data
        response = self._make_request('PUT', endpoint, **kwargs)
        return self._handle_response(response)

    def patch(self, endpoint: str, data: Dict[str, Any] = None, **kwargs) -> Dict[str, Any]:
        """PATCH request"""
        if data:
            kwargs['json'] = data
        response = self._make_request('PATCH', endpoint, **kwargs)
        return self._handle_response(response)

    def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """DELETE request"""
        response = self._make_request('DELETE', endpoint, **kwargs)
        return self._handle_response(response)

