#!/usr/bin/env python3
"""
Run the Casino Rewards Platform API
"""

import uvicorn

if __name__ == "__main__":
    print("Starting Casino Rewards Platform API...")
    print("API will be available at: http://localhost:8000")
    print("API docs will be available at: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    
    uvicorn.run(
        "api.main:app", 
        host="0.0.0.0", 
        port=8000,
        reload=True,
        log_level="info"
    )