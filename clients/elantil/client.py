"""
Generic Elantil API client for service-to-service authentication
"""

import logging
import sys
from pathlib import Path

# Add config to the path
config_path = Path(__file__).parent.parent
sys.path.insert(0, str(config_path))

from auth.keycloak import KeycloakTokenManager
from utils.rest_client import APIClient
from config.manager import get_config
from utils.exceptions import AuthenticationError

logger = logging.getLogger(__name__)


class ElantilClient:
    """Generic Elantil API client for service-to-service authentication"""
    
    def __init__(self,
                 client_id: str = None,
                 client_secret: str = None,
                 environment: str = None
                 ):
        """
        Initialize Elantil client
        
        Args:
            client_id: OAuth2 client ID (optional, can be set in config)
            client_secret: OAuth2 client secret for service-to-service authentication
            environment: Environment name (local, dev, prod)
        """
        
        # Load configuration
        self.config = get_config(environment)
        self.config.validate_elantil_config()
        
        # Get configuration values
        elantil_config = self.config.get_elantil_config()
        
        # Use provided client_secret or validate configuration has it
        client_secret = client_secret or elantil_config.get("client_secret")
        client_id = client_id or elantil_config.get("client_id")
        
        if not client_secret:
            raise AuthenticationError("Client secret must be provided either as parameter or in configuration")
        
        if not client_id:
            raise AuthenticationError("Client ID must be provided either as parameter or in configuration")
        
        # Setup logging
        self.config.setup_logging()

        # Initialize authentication with Client Credentials flow
        self.token_manager = KeycloakTokenManager(
            auth_url=elantil_config["auth_url"],
            client_id=client_id,
            client_secret=client_secret
        )
        
        # Initialize API client
        self.api_client = APIClient(
            token_manager=self.token_manager,
            base_url=elantil_config["api_url"],
            tenant_id=elantil_config["tenant_id"]
        )
        
        # Store tenant integration ID for services that need it
        self.tenant_integration_id = elantil_config.get("tenant_integration_id")
        if not self.tenant_integration_id:
            raise ValueError("Tenant integration ID must be provided in Elantil configuration")
        
        logger.info(f"Elantil client initialized for environment: {self.config.environment}")
    
    def test_authentication(self) -> bool:
        """Test authentication with Elantil"""
        try:
            token = self.token_manager.get_valid_token()
            if token:
                logger.info("Authentication test successful")
                return True
            return False
        except Exception as e:
            logger.error(f"Authentication test failed: {e}")
            return False
    
    def get_client_info(self) -> dict:
        """Get information about the client configuration"""
        elantil_config = self.config.get_elantil_config()
        token_info = self.token_manager.get_token_info()
        
        return {
            "environment": self.config.environment,
            "api_url": elantil_config["api_url"],
            "tenant_id": elantil_config["tenant_id"],
            "authentication": token_info
        }
    
    # Generic API methods
    
    def get(self, endpoint: str, params: dict = None):
        """Make a GET request to the API"""
        return self.api_client.get(endpoint, params)
    
    def post(self, endpoint: str, data: dict = None):
        """Make a POST request to the API"""
        return self.api_client.post(endpoint, data)
    
    def put(self, endpoint: str, data: dict = None):
        """Make a PUT request to the API"""
        return self.api_client.put(endpoint, data)
    
    def patch(self, endpoint: str, data: dict = None):
        """Make a PATCH request to the API"""
        return self.api_client.patch(endpoint, data)
    
    def delete(self, endpoint: str):
        """Make a DELETE request to the API"""
        return self.api_client.delete(endpoint)
