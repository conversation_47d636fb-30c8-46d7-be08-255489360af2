# Deployment Guide

Quick guide for deploying the Casino Rewards Platform.

## Prerequisites

- Python 3.11+
- Access to Elantil API with valid client credentials
- MonkeyTilt tenant access

## Quick Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Authentication**
   ```bash
   export ELANTIL_CLIENT_SECRET=your_client_secret
   export ELANTIL_CLIENT_ID=your_client_id
   ```

3. **Test Installation**
   ```bash
   python examples/auth_test.py
   ```

## Usage Examples

### Basic Free Spin Operations
```bash
# Test authentication
python examples/auth_test.py

# Basic template creation and assignment
python examples/basic_usage.py

# Test webhook endpoint
python examples/test_webhook.py
```

## Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `ELANTIL_CLIENT_SECRET` | Yes | OAuth2 client secret |
| `ELANTIL_CLIENT_ID` | No | OAuth2 client ID (optional if in config) |
| `ENVIRONMENT` | No | Environment name (local/dev/prod) |
| `LOG_LEVEL` | No | Logging level (DEBUG/INFO/WARNING) |

## API Server

Start the API server:
```bash
python run_api.py
```

The API will be available at http://localhost:8000 with documentation at http://localhost:8000/docs

## Docker Deployment

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
ENV PYTHONPATH=/app
CMD ["python", "run_api.py"]
```

```bash
docker build -t casino-rewards .
docker run -e ELANTIL_CLIENT_SECRET=your_secret casino-rewards
```

## Configuration Files

Update `config/environments/local.yaml` with your credentials for local development.

## Production Deployment

### Service Architecture
- **Clients**: Generic API clients for external services
- **Services**: Business logic separated from API communication
- **Configuration**: Environment-specific YAML configurations

### Monitoring
```bash
# Health check
python examples/auth_test.py

# Basic usage and functionality test
python examples/basic_usage.py
```

### Testing
```bash
# Run all tests
python -m pytest tests/

# Run specific test suites
python -m pytest tests/test_freespins.py
python -m pytest tests/test_auth.py
```