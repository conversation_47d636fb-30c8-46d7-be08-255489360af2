# Casino Rewards Platform

Python platform for managing casino reward promotions through Elantil's API with OAuth2 authentication.

## Features

- **Free Spin Management**: Create, assign, and manage free spin promotions
- **OAuth2 Authentication**: Client Credentials flow for service-to-service authentication
- **Template System**: Game-specific, provider-specific, and arbitrary free spin templates
- **Bulk Operations**: Efficient batch assignment to multiple players
- **REST API**: Webhook endpoints for external integrations (Optimove)
- **Configuration Management**: Environment-based YAML configs with overrides
- **Clean Architecture**: Separation of API clients and business services

## Quick Start

### Prerequisites

- Python 3.11+
- Elantil API access with client credentials
- MonkeyTilt tenant access

### Installation

```bash
git clone <repository-url>
cd casino-rewards
pip install -r requirements.txt
```

### Configuration

Set credentials in `config/environments/local.yaml` or use environment variables:

```bash
export ELANTIL_CLIENT_SECRET=your_client_secret
export ELANTIL_CLIENT_ID=your_client_id
```

### Basic Usage

```python
from clients.elantil.client import ElantilClient
from services.freespins.service import FreeSpinService

# Initialize client
client = ElantilClient(environment="local")

# Test authentication
if client.test_authentication():
    print("Authentication successful")

# Initialize service
freespin_service = FreeSpinService(client, client.tenant_integration_id)

# Create template
template = freespin_service.create_game_specific_template(
    game_id="evo_dragontiger",
    spin_count=5,
    total_value=25.0
)

# Assign free spins
assignment = freespin_service.assign_free_spins(template.key, "player_123")
```

## Project Structure

```
casino-rewards/
├── auth/                    # OAuth2 authentication
├── clients/elantil/         # Generic API client
├── services/                # Business logic services
│   ├── base.py             # Base classes and enums
│   ├── freespins/          # Free spin service
│   └── bonuses/            # Bonus service (future)
├── config/                  # Environment configuration
├── api/                     # REST API endpoints
├── utils/                   # Shared utilities
├── tests/                   # Test suite
├── examples/                # Usage examples
└── docs/                    # Documentation
```

## Free Spin Types

### Game-Specific
```python
template = freespin_service.create_game_specific_template(
    game_id="gates_of_olympus_1000",
    spin_count=15,
    total_value=30.0
)
```

### Provider-Specific
```python
template = freespin_service.create_provider_specific_template(
    provider_id="pragmatic",
    spin_count=12,
    total_value=25.0
)
```

### Arbitrary
```python
template = freespin_service.create_arbitrary_template(
    spin_count=25,
    total_value=50.0
)
```

## Operations

### Template Management
```python
# Create, list, update, delete templates
templates = freespin_service.list_templates()
template = freespin_service.get_template("template_key")
```

### Assignment Management
```python
# Single assignment
assignment = freespin_service.assign_free_spins("template_key", "player_123")

# Bulk assignment
player_ids = ["player_1", "player_2", "player_3"]
assignments = freespin_service.bulk_assign_free_spins("template_key", player_ids)

# Cancel/forfeit
freespin_service.cancel_assignment("assignment_id")
freespin_service.forfeit_assignment("assignment_id")
```

### Monitoring
```python
# Statistics
stats = freespin_service.get_assignment_statistics()

# Player assignments
player_assignments = freespin_service.get_player_assignments("player_123")
```

## API Server

Start the REST API server:

```bash
python run_api.py
```

API available at http://localhost:8000 with documentation at http://localhost:8000/docs

### Endpoints

- `POST /optimove/webhook` - Optimove webhook handler
- `GET /health` - Health check endpoint

## Testing

```bash
# Test authentication
python examples/auth_test.py

# Basic usage example
python examples/basic_usage.py

# Test webhook endpoint
python examples/test_webhook.py

# Run full test suite
python -m pytest tests/
```

## Configuration

### Environment Variables

| Variable | Required | Description |
|----------|----------|-------------|
| `ELANTIL_CLIENT_SECRET` | Yes | OAuth2 client secret |
| `ELANTIL_CLIENT_ID` | No | OAuth2 client ID |
| `ENVIRONMENT` | No | Environment (local/dev/prod) |
| `LOG_LEVEL` | No | Logging level |

### Configuration Files

Environment-specific YAML configurations in `config/environments/`:
- `local.yaml` - Local development
- `dev.yaml` - Development environment
- `prod.yaml` - Production environment

## Architecture

### Client Layer
- **ElantilClient**: Generic API client for authentication and HTTP requests
- **OAuth2 Authentication**: Client Credentials flow for service-to-service auth

### Service Layer
- **FreeSpinService**: Business logic for free spin operations
- **Base Classes**: Shared models and types for all reward services

### Error Handling

Custom exception hierarchy for comprehensive error handling:

```python
from utils.exceptions import (
    AuthenticationError,
    FreeSpinError,
    TemplateError,
    APIError
)
```

## Deployment

See `docs/DEPLOYMENT.md` for detailed deployment instructions including Docker support.

## Dependencies

- `requests>=2.28.0` - HTTP client
- `pyyaml>=6.0` - YAML configuration
- `pydantic>=2.0.0` - Data validation
- `fastapi>=0.116.0` - REST API framework
- `uvicorn>=0.35.0` - ASGI server