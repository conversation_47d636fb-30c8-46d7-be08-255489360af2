#!/usr/bin/env python3
"""
Authentication test only
"""

import os
import sys
from pathlib import Path

# Add the project root to the path
project_path = Path(__file__).parent.parent
sys.path.insert(0, str(project_path))

from clients.elantil.client import ElantilClient


def main():
    """Test authentication only"""
    
    print("Casino Rewards Platform - Authentication Test")
    print("=" * 50)
    
    try:
        # Initialize client
        print("\nInitializing client...")
        client = ElantilClient(environment="local")
        print("Client initialized successfully")
        
        # Test authentication
        print("\nTesting authentication...")
        if client.test_authentication():
            print("Authentication successful!")
            
            # Get client info
            client_info = client.get_client_info()
            print(f"\nClient Information:")
            print(f"Environment: {client_info['environment']}")
            print(f"API URL: {client_info['api_url']}")
            print(f"Tenant ID: {client_info['tenant_id']}")
            print(f"Auth Type: {client_info['authentication']['authentication_type']}")
            
        else:
            print("Authentication failed")
            return
        
        print("\nAuthentication test completed successfully!")
        
    except Exception as e:
        print(f"\nError occurred: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()