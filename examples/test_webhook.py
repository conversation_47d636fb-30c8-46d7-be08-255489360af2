#!/usr/bin/env python3
"""
Test the Optimove webhook endpoint
"""

import requests
import json

def test_webhook():
    """Test the Optimove webhook endpoint"""
    
    print("Testing Optimove Webhook Endpoint")
    print("=" * 40)
    
    # Test data
    webhook_data = {
        "call_id": "12345678",
        "player_id": "87654321", 
        "player_name": "<PERSON>",
        "enable_logging": True
    }
    
    # Endpoint URL (assuming running on localhost:8000)
    url = "http://localhost:8000/optimove/webhook"
    
    try:
        print(f"Sending POST request to: {url}")
        print(f"Data: {json.dumps(webhook_data, indent=2)}")
        
        response = requests.post(url, json=webhook_data)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Body: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("\nWebhook test successful!")
        else:
            print(f"\nWebhook test failed with status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("\nCould not connect to API server.")
        print("Make sure the API server is running with: uvicorn api.app:app --reload")
    except Exception as e:
        print(f"\nError: {e}")

if __name__ == "__main__":
    test_webhook()