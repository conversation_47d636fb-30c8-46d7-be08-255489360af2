#!/usr/bin/env python3
"""
Simplified basic usage example - Authentication and Template Creation only
"""

import sys
from pathlib import Path

# Add the project root to the path
project_path = Path(__file__).parent.parent
sys.path.insert(0, str(project_path))

from clients.elantil.client import ElantilClient
from services.freespins.service import FreeSpinService


def main():
    """Simple example - just authentication and template creation"""

    print("Casino Rewards Platform - Simple Example")
    print("=" * 45)

    try:
        # Step 1: Initialize client (uses the config file with credentials)
        print("\nStep 1: Initializing client...")
        client = ElantilClient(environment="local")
        print("Client initialized successfully")

        # Step 2: Test authentication
        print("\nStep 2: Testing authentication...")
        if client.test_authentication():
            print("Authentication successful")
        else:
            print("Authentication failed")
            return

        # Step 3: Create a simple template using FreeSpinService
        print("\nStep 3: Creating free spin template...")

        try:
            # Initialize FreeSpinService with the client
            freespin_service = FreeSpinService(client, client.tenant_integration_id)
            
            # Create a game-specific template
            game_template = freespin_service.create_game_specific_template(
                game_id="evo_dragontiger",
                spin_count=1,
                total_value=50.0,
                currency_code="USD",
                expiry_hours=24
            )
            print(f"Template created successfully: {game_template.key}")
            print(f"Template name: {game_template.name}")

        except Exception as template_error:
            print(f"Template creation failed: {template_error}")
            return

        print("\nBasic operations completed successfully!")

    except Exception as e:
        print(f"\nError occurred: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()