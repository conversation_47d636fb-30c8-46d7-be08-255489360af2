"""
Configuration management for the Elantil platform
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional


class ConfigurationError(Exception):
    """Raised when configuration is invalid"""
    pass


logger = logging.getLogger(__name__)


class ConfigManager:
    """Configuration manager that loads from YAML files and environment variables"""
    
    def __init__(self, environment: str = None):
        self.environment = environment or os.getenv("ENVIRONMENT", "local")
        self._config_data: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from YAML file and environment variables"""
        
        # Find the config file
        config_dir = Path(__file__).parent / "environments"
        config_file = config_dir / f"{self.environment}.yaml"
        
        if not config_file.exists():
            raise ConfigurationError(f"Configuration file not found: {config_file}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self._config_data = yaml.safe_load(f) or {}
                
            logger.debug(f"Loaded configuration for environment: {self.environment}")
            
        except yaml.YAMLError as e:
            raise ConfigurationError(f"Error parsing YAML configuration: {e}")
        except Exception as e:
            raise ConfigurationError(f"Error loading configuration: {e}")
        
        # Override with environment variables
        self._load_env_overrides()
    
    def _load_env_overrides(self) -> None:
        """Load environment variable overrides"""
        
        # Elantil configuration overrides
        if os.getenv("ELANTIL_AUTH_URL"):
            self._config_data.setdefault("elantil", {})["auth_url"] = os.getenv("ELANTIL_AUTH_URL")
        
        if os.getenv("ELANTIL_API_URL"):
            self._config_data.setdefault("elantil", {})["api_url"] = os.getenv("ELANTIL_API_URL")
        
        if os.getenv("ELANTIL_CLIENT_ID"):
            self._config_data.setdefault("elantil", {})["client_id"] = os.getenv("ELANTIL_CLIENT_ID")
        
        if os.getenv("ELANTIL_CLIENT_SECRET"):
            self._config_data.setdefault("elantil", {})["client_secret"] = os.getenv("ELANTIL_CLIENT_SECRET")
        
        if os.getenv("ELANTIL_TENANT_ID"):
            self._config_data.setdefault("elantil", {})["tenant_id"] = os.getenv("ELANTIL_TENANT_ID")

        if os.getenv("ELANTIL_TENANT_INTEGRATION_ID"):
            self._config_data.setdefault("elantil", {})["tenant_integration_id"] = os.getenv("ELANTIL_TENANT_INTEGRATION_ID")

        # Logging overrides
        if os.getenv("LOG_LEVEL"):
            self._config_data.setdefault("logging", {})["level"] = os.getenv("LOG_LEVEL")
        
        if os.getenv("LOG_FORMAT"):
            self._config_data.setdefault("logging", {})["format"] = os.getenv("LOG_FORMAT")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by dot-notation key (e.g., 'elantil.auth_url')"""
        
        keys = key.split('.')
        value = self._config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_elantil_config(self) -> Dict[str, str]:
        """Get Elantil-specific configuration"""
        elantil_config = self._config_data.get("elantil", {})
        
        return {
            "auth_url": elantil_config.get("auth_url"),
            "api_url": elantil_config.get("api_url"),
            "client_id": elantil_config.get("client_id"),
            "client_secret": elantil_config.get("client_secret"),
            "tenant_id": elantil_config.get("tenant_id"),
            "tenant_integration_id": elantil_config.get("tenant_integration_id")
        }
    
    def get_freespin_config(self) -> Dict[str, Any]:
        """Get free spin configuration"""
        return self._config_data.get("freespins", {})
    
    def get_logging_config(self) -> Dict[str, str]:
        """Get logging configuration"""
        return self._config_data.get("logging", {})
    
    def get_api_config(self) -> Dict[str, Any]:
        """Get API client configuration"""
        return self._config_data.get("api", {})
    
    def validate_elantil_config(self) -> None:
        """Validate that the required Elantil configuration is present"""
        elantil_config = self.get_elantil_config()
        
        required_keys = ["auth_url", "api_url", "client_id", "client_secret", "tenant_id", "tenant_integration_id"]
        missing_keys = [key for key in required_keys if not elantil_config.get(key)]
        
        if missing_keys:
            raise ConfigurationError(f"Missing required Elantil configuration: {missing_keys}")
    
    @staticmethod
    def validate_client_secret(client_secret: str) -> None:
        """Validate client secret is provided"""
        if not client_secret or not client_secret.strip():
            raise ConfigurationError("Client secret is required but not provided")
    
    def setup_logging(self) -> None:
        """Setup logging based on configuration"""
        logging_config = self.get_logging_config()
        
        level = logging_config.get("level", "INFO").upper()
        format_type = logging_config.get("format", "detailed")
        
        # Set the logging level
        numeric_level = getattr(logging, level, logging.INFO)
        
        # Configure format
        if format_type == "json":
            log_format = '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}'
        else:
            log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        logging.basicConfig(
            level=numeric_level,
            format=log_format,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        logger.info(f"Logging configured: level={level}, format={format_type}")


# Global config instance
_config_instance: Optional[ConfigManager] = None


def get_config(environment: str = None) -> ConfigManager:
    """Get configuration instance (singleton pattern)"""
    global _config_instance
    
    if environment is None:
        environment = os.getenv("ENVIRONMENT", "local")
    
    if _config_instance is None or _config_instance.environment != environment:
        _config_instance = ConfigManager(environment)
    
    return _config_instance


def setup_logging(environment: str = None) -> None:
    """Setup logging for the application"""
    config = get_config(environment)
    config.setup_logging()

