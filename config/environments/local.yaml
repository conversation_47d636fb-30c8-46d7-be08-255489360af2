# Local development configuration
elantil:
  auth_url: "https://keycloak.central-dev.monkeytilt.codes/realms/monkeytilt/protocol/openid-connect/token"
  api_url: "https://api.p-dev.monkeytilt.codes"
  client_id: "casino-rewards"
  client_secret: "KFScScrm5HgC5pJjiga2Tc5cdXSIxGGS"
  tenant_id: "monkeytilt"
  tenant_integration_id: "694fe7b0-38e5-57d2-94a5-62083f93193c"
  
# Default free spin settings
freespins:
  default_expiry_hours: 24
  default_currency: "USD"
  
# Logging configuration
logging:
  level: "DEBUG"
  format: "detailed"
  
# API client settings
api:
  timeout: 30
  retry_attempts: 3
  retry_delay: 1

optimove:
  url: "https://api5.optimove.net/"
  api_key: "68548530e72b67cf1e305b79527d80b8e9692d10f6fd5fc6e2f9"