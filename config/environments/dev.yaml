# Development environment configuration
elantil:
  auth_url: "https://keycloak.central-dev.monkeytilt.codes/realms/monkeytilt/protocol/openid-connect/token"
  api_url: "https://api.p-dev.monkeytilt.codes"
  client_id: "bo"
  tenant_id: "monkeytilt"
  
# Default free spin settings
freespins:
  default_expiry_hours: 24
  default_currency: "USD"
  tenant_integration_id: "694fe7b0-38e5-57d2-94a5-62083f93193c"
  
# Logging configuration
logging:
  level: "INFO"
  format: "json"
  
# API client settings
api:
  timeout: 30
  retry_attempts: 3
  retry_delay: 2

