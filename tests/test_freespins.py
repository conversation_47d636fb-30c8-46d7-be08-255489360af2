"""
Tests for free spin functionality.
"""

import os
import sys
from unittest.mock import Mock
from datetime import datetime

# Add src to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from services.freespins.models import FreeSpinType, FreeSpinConfig, FreeSpinTemplate
from services.freespins.service import FreeSpinService


class TestFreeSpinTypes:
    """Test the free spin types and models."""
    
    def test_free_spin_config_validation(self):
        """Test the free spin configuration validation."""
        
        # Test a valid arbitrary config
        config = FreeSpinConfig(
            spin_type=FreeSpinType.ARBITRARY,
            spin_count=10,
            total_value=20.0
        )
        assert config.spin_type == FreeSpinType.ARBITRARY
        assert config.spin_count == 10
        assert config.total_value == 20.0
        
        # Test a game-specific config
        config = FreeSpinConfig(
            spin_type=FreeSpinType.GAME_SPECIFIC,
            spin_count=5,
            total_value=10.0,
            game_id="test_game"
        )
        assert config.game_id == "test_game"
        
        # Test a multi-game config
        config = FreeSpinConfig(
            spin_type=FreeSpinType.MULTI_GAME,
            spin_count=8,
            total_value=15.0,
            game_list=["game1", "game2"]
        )
        assert config.game_list == ["game1", "game2"]
    
    def test_free_spin_template_elantil_format(self):
        """Test the template conversion to Elantil format."""
        
        config = FreeSpinConfig(
            spin_type=FreeSpinType.GAME_SPECIFIC,
            spin_count=10,
            total_value=20.0,
            game_id="test_game"
        )
        
        template = FreeSpinTemplate(
            key="test_template",
            name="Test Template",
            config=config
        )
        
        elantil_data = template.to_elantil_format()
        
        assert elantil_data["data"]["type"] == "promotion-templates"
        assert elantil_data["data"]["attributes"]["key"] == "test_template"
        assert elantil_data["data"]["attributes"]["name"] == "Test Template"
        assert elantil_data["data"]["attributes"]["type"] == "FreeSpins"
        assert elantil_data["data"]["attributes"]["count"] == 10
        assert elantil_data["data"]["attributes"]["amount"] == "20.0"


class TestFreeSpinService:
    """Test the free spin service."""
    
    def setup_method(self):
        """Set up the test fixtures."""
        self.mock_elantil_client = Mock()
        self.freespin_service = FreeSpinService(self.mock_elantil_client, "test_tenant_id")
    
    def test_create_template(self):
        """Test the template creation."""
        
        # Mock the API response
        self.mock_elantil_client.post.return_value = {
            "data": {"id": "template_123"}
        }
        
        config = FreeSpinConfig(
            spin_type=FreeSpinType.ARBITRARY,
            spin_count=10,
            total_value=20.0
        )
        
        template = self.freespin_service.create_template(config, "test_key", "Test Template")
        
        assert template.key == "test_key"
        assert template.name == "Test Template"
        assert template.template_id == "template_123"
        
        # Verify the API call
        self.mock_elantil_client.post.assert_called_once()
        call_args = self.mock_elantil_client.post.call_args
        assert call_args[0][0] == "promotions/v1/promotion-templates"
    
    def test_create_game_specific_template(self):
        """Test creating a game-specific template."""
        
        self.mock_elantil_client.post.return_value = {"data": {"id": "template_123"}}
        
        template = self.freespin_service.create_game_specific_template(
            game_id="test_game",
            spin_count=5,
            total_value=10.0
        )
        
        assert template.config.spin_type == FreeSpinType.GAME_SPECIFIC
        assert template.config.game_id == "test_game"
        assert template.config.spin_count == 5
    
    def test_template_key_generation(self):
        """Test the template key generation."""
        
        config = FreeSpinConfig(
            spin_type=FreeSpinType.GAME_SPECIFIC,
            spin_count=10,
            total_value=20.0,
            game_id="test_game"
        )
        
        key = self.freespin_service._generate_template_key(config)
        
        assert "game_test_game_10_20" in key
        assert len(key) > 20  # Should include a timestamp
    
    def test_template_name_generation(self):
        """Test the template name generation."""
        
        config = FreeSpinConfig(
            spin_type=FreeSpinType.PROVIDER_SPECIFIC,
            spin_count=15,
            total_value=30.0,
            provider_id="pragmatic"
        )
        
        name = self.freespin_service._generate_template_name(config)
        
        assert "15 Free Spins ($30.0) - pragmatic Games" == name


class TestFreeSpinAssignment:
    """Test the free spin assignment operations."""
    
    def setup_method(self):
        """Set up the test fixtures."""
        self.mock_elantil_client = Mock()
        self.freespin_service = FreeSpinService(self.mock_elantil_client, "test_tenant_id")
    
    def test_assign_free_spins(self):
        """Test the free spin assignment."""
        
        # Mock the API response
        self.mock_elantil_client.post.return_value = {
            "data": {
                "id": "assignment_123",
                "attributes": {
                    "templateKey": "test_template",
                    "ownerId": "player_123",
                    "ownerType": "player",
                    "status": "active",
                    "spinsRemaining": 10,
                    "totalValue": "20.0",
                    "currencyCode": "USD",
                    "createdAt": "2025-01-01T00:00:00Z",
                    "spinsUsed": 0,
                    "amountConverted": "0.0"
                }
            }
        }
        
        assignment = self.freespin_service.assign_free_spins("test_template", "player_123")
        
        assert assignment.assignment_id == "assignment_123"
        assert assignment.template_key == "test_template"
        assert assignment.owner_id == "player_123"
        assert assignment.status == "active"
        assert assignment.spins_remaining == 10
        
        # Verify the API call
        self.mock_elantil_client.post.assert_called_once()
        call_args = self.mock_elantil_client.post.call_args
        assert "test_template/assign" in call_args[0][0]
    
    def test_cancel_assignment(self):
        """Test the assignment cancellation."""
        
        self.mock_elantil_client.post.return_value = {}
        
        result = self.freespin_service.cancel_assignment("assignment_123")
        
        assert result is True
        
        # Verify the API call
        self.mock_elantil_client.post.assert_called_once()
        call_args = self.mock_elantil_client.post.call_args
        assert "assignment_123/cancel" in call_args[0][0]
    
    def test_bulk_assign_free_spins(self):
        """Test the bulk assignment."""
        
        # Mock the successful responses
        self.mock_elantil_client.post.return_value = {
            "data": {
                "id": "assignment_123",
                "attributes": {
                    "templateKey": "test_template",
                    "ownerId": "player_123",
                    "ownerType": "player",
                    "status": "active",
                    "spinsRemaining": 10,
                    "totalValue": "20.0",
                    "currencyCode": "USD",
                    "createdAt": "2025-01-01T00:00:00Z",
                    "spinsUsed": 0,
                    "amountConverted": "0.0"
                }
            }
        }
        
        player_ids = ["player_1", "player_2", "player_3"]
        assignments = self.freespin_service.bulk_assign_free_spins("test_template", player_ids)
        
        assert len(assignments) == 3
        assert all(a.template_key == "test_template" for a in assignments)
        
        # Verify the API calls
        assert self.mock_elantil_client.post.call_count == 3


def run_all_tests():
    """Run all the free spin tests."""
    
    print("Running the free spin tests...")
    
    try:
        # Test the free spin types
        type_tests = TestFreeSpinTypes()
        type_tests.test_free_spin_config_validation()
        print("Free spin config validation test passed")
        
        type_tests.test_free_spin_template_elantil_format()
        print("Template Elantil format test passed")
        
        # Test the free spin service
        service_tests = TestFreeSpinService()
        service_tests.setup_method()
        service_tests.test_create_template()
        print("Template creation test passed")
        
        service_tests.setup_method()
        service_tests.test_create_game_specific_template()
        print("Game-specific template test passed")
        
        service_tests.setup_method()
        service_tests.test_template_key_generation()
        print("Template key generation test passed")
        
        service_tests.setup_method()
        service_tests.test_template_name_generation()
        print("Template name generation test passed")
        
        # Test the free spin assignment
        assignment_tests = TestFreeSpinAssignment()
        assignment_tests.setup_method()
        assignment_tests.test_assign_free_spins()
        print("Free spin assignment test passed")
        
        assignment_tests.setup_method()
        assignment_tests.test_cancel_assignment()
        print("Assignment cancellation test passed")
        
        assignment_tests.setup_method()
        assignment_tests.test_bulk_assign_free_spins()
        print("Bulk assignment test passed")
        
        print("\nAll the free spin tests passed!")
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_all_tests()

