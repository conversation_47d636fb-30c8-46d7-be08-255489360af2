"""
Tests for authentication functionality
"""

import pytest
import os
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

# Add src to path for imports
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from auth.keycloak import KeycloakTokenManager
from auth.keycloak import AuthenticationError


class TestKeycloakTokenManager:
    """Test Keycloak token manager"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.auth_url = "https://test-keycloak.com/token"
        self.client_id = "test-client"
        self.client_secret = "test-client-secret"
        
        self.token_manager = KeycloakTokenManager(
            auth_url=self.auth_url,
            client_id=self.client_id,
            client_secret=self.client_secret
        )
    
    @patch('requests.Session.post')
    def test_successful_client_credentials(self, mock_post):
        """Test successful client credentials authentication"""
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'new-access-token',
            'token_type': 'Bearer',
            'expires_in': 3600
        }
        mock_post.return_value = mock_response
        
        # Test token refresh
        self.token_manager._refresh_token()
        
        # Verify results
        assert self.token_manager.access_token == 'new-access-token'
        assert self.token_manager.expires_at is not None
        
        # Verify API call
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[0][0] == self.auth_url
        assert call_args[1]['data']['grant_type'] == 'client_credentials'
        assert call_args[1]['data']['client_id'] == 'test-client'
        assert call_args[1]['data']['client_secret'] == 'test-client-secret'
    
    @patch('requests.Session.post')
    def test_failed_client_credentials(self, mock_post):
        """Test failed client credentials authentication"""
        
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            'error': 'invalid_client',
            'error_description': 'Invalid client credentials'
        }
        mock_response.content = True
        mock_post.return_value = mock_response
        
        # Test authentication failure
        with pytest.raises(AuthenticationError) as exc_info:
            self.token_manager._refresh_token()
        
        assert "Invalid client credentials" in str(exc_info.value)
    
    def test_token_expiry_check(self):
        """Test token expiry checking"""
        
        # Test with no expiry time
        assert self.token_manager._is_token_expired() is True
        
        # Test with future expiry
        self.token_manager.expires_at = datetime.now() + timedelta(hours=1)
        assert self.token_manager._is_token_expired() is False
        
        # Test with past expiry
        self.token_manager.expires_at = datetime.now() - timedelta(hours=1)
        assert self.token_manager._is_token_expired() is True
        
        # Test with expiry within buffer time
        self.token_manager.expires_at = datetime.now() + timedelta(seconds=200)  # Less than 5 min buffer
        assert self.token_manager._is_token_expired() is True
    
    @patch('requests.Session.post')
    def test_get_valid_token(self, mock_post):
        """Test getting valid token"""
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'valid-token',
            'expires_in': 3600
        }
        mock_post.return_value = mock_response
        
        # Test getting valid token
        token = self.token_manager.get_valid_token()
        
        assert token == 'valid-token'
        assert self.token_manager.access_token == 'valid-token'
    
    
    def test_get_token_info(self):
        """Test getting token information"""
        
        # Test with no token
        info = self.token_manager.get_token_info()
        assert info['has_access_token'] is False
        assert info['authentication_type'] == 'client_credentials'
        assert info['is_expired'] is True
        
        # Test with valid token
        self.token_manager.access_token = "test-token"
        self.token_manager.expires_at = datetime.now() + timedelta(hours=1)
        
        info = self.token_manager.get_token_info()
        assert info['has_access_token'] is True
        assert info['authentication_type'] == 'client_credentials'
        assert info['is_expired'] is False
        assert info['expires_at'] is not None


if __name__ == "__main__":
    # Run tests
    test_manager = TestKeycloakTokenManager()
    
    print("Running authentication tests...")
    
    try:
        test_manager.setup_method()
        test_manager.test_token_expiry_check()
        print("Token expiry check test passed")
        
        test_manager.setup_method()
        test_manager.test_get_token_info()
        print("Get token info test passed")
        
        print("\nAll authentication tests passed!")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()

