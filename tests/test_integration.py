"""
Integration tests for the complete Elantil platform
"""

import os
import sys
from unittest.mock import Mock, patch

# Add src to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from clients.elantil.client import ElantilClient
from services.freespins.service import FreeSpinService
from config.manager import get_config


class MockElantilIntegrationTest:
    """Integration test with mocked Elantil API"""
    
    def __init__(self):
        self.test_client_secret = "test_client_secret_12345"
        self.test_client_id = "test_client_id"
        self.mock_responses = self._setup_mock_responses()
    
    def _setup_mock_responses(self):
        """Setup mock API responses"""
        return {
            'auth_success': {
                'access_token': 'mock_access_token_12345',
                'token_type': 'Bearer',
                'expires_in': 3600
            },
            'template_create': {
                'data': {
                    'id': 'template_12345',
                    'type': 'promotion-templates',
                    'attributes': {
                        'key': 'test_template_key',
                        'name': 'Test Template',
                        'isEnabled': True
                    }
                }
            },
            'assignment_create': {
                'data': {
                    'id': 'assignment_12345',
                    'type': 'promotion-assignments',
                    'attributes': {
                        'templateKey': 'test_template_key',
                        'ownerId': 'player_12345',
                        'ownerType': 'player',
                        'status': 'active',
                        'spinsRemaining': 10,
                        'totalValue': '20.0',
                        'currencyCode': 'USD',
                        'createdAt': '2025-01-01T00:00:00Z',
                        'spinsUsed': 0,
                        'amountConverted': '0.0'
                    }
                }
            },
            'assignment_get': {
                'data': {
                    'id': 'assignment_12345',
                    'type': 'promotion-assignments',
                    'attributes': {
                        'templateKey': 'test_template_key',
                        'ownerId': 'player_12345',
                        'ownerType': 'player',
                        'status': 'active',
                        'spinsRemaining': 8,
                        'totalValue': '20.0',
                        'currencyCode': 'USD',
                        'createdAt': '2025-01-01T00:00:00Z',
                        'expiresAt': '2025-01-02T00:00:00Z',
                        'spinsUsed': 2,
                        'amountConverted': '4.0'
                    }
                }
            },
            'templates_list': {
                'data': [
                    {
                        'id': 'template_12345',
                        'type': 'promotion-templates',
                        'attributes': {
                            'key': 'test_template_key',
                            'name': 'Test Template',
                            'isEnabled': True,
                            'count': 10,
                            'amount': '20.0',
                            'currencyCode': 'USD'
                        }
                    }
                ]
            }
        }
    
    @patch.dict(os.environ, {'ELANTIL_CLIENT_SECRET': 'test_secret'})
    @patch('requests.Session.post')
    @patch('requests.Session.request')
    def test_complete_workflow(self, mock_request, mock_post):
        """Test complete workflow from authentication to free spin management"""
        
        print("Starting complete workflow integration test...")
        
        # Setup mocks
        def mock_post_side_effect(url, **kwargs):
            mock_response = Mock()
            mock_response.status_code = 200
            
            if 'token' in url:
                mock_response.json.return_value = self.mock_responses['auth_success']
            elif 'assign' in url:
                mock_response.json.return_value = self.mock_responses['assignment_create']
            elif 'cancel' in url or 'forfeit' in url:
                mock_response.json.return_value = {}
            else:
                mock_response.json.return_value = self.mock_responses['template_create']
            
            return mock_response
        
        def mock_request_side_effect(method, url, **kwargs):
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.content = True
            
            if method == 'GET':
                if 'promotion-templates' in url and not any(x in url for x in ['assign', 'cancel']):
                    mock_response.json.return_value = self.mock_responses['templates_list']
                elif 'owner-promotions' in url:
                    # Return a list of assignments for list_assignments
                    mock_response.json.return_value = {
                        'data': [self.mock_responses['assignment_get']['data']]
                    }
                else:
                    mock_response.json.return_value = self.mock_responses['assignment_get']
            elif method == 'POST':
                if 'assign' in url:
                    mock_response.json.return_value = self.mock_responses['assignment_create']
                elif 'cancel' in url or 'forfeit' in url:
                    mock_response.json.return_value = {}
                else:
                    mock_response.json.return_value = self.mock_responses['template_create']
            
            return mock_response
        
        mock_post.side_effect = mock_post_side_effect
        mock_request.side_effect = mock_request_side_effect
        
        try:
            # Step 1: Initialize client and service
            print("  Step 1: Initializing Elantil client...")
            client = ElantilClient(environment="local")
            freespin_service = FreeSpinService(client, client.tenant_integration_id)
            print("  Client and service initialized successfully")
            
            # Step 2: Test authentication
            print("  Step 2: Testing authentication...")
            auth_result = client.test_authentication()
            assert auth_result is True, "Authentication should succeed"
            print("  Authentication successful")
            
            # Step 3: Create templates
            print("  Step 3: Creating free spin templates...")
            
            # Create game-specific template
            game_template = freespin_service.create_game_specific_template(
                game_id="gates_of_olympus_1000",
                spin_count=10,
                total_value=20.0
            )
            assert game_template is not None, "Game template should be created"
            print(f"  Game-specific template created: {game_template.key}")
            
            # Create provider template
            provider_template = freespin_service.create_provider_specific_template(
                provider_id="pragmatic",
                spin_count=15,
                total_value=30.0
            )
            assert provider_template is not None, "Provider template should be created"
            print(f"  Provider template created: {provider_template.key}")
            
            # Step 4: Assign free spins
            print("  Step 4: Assigning free spins to players...")
            
            assignment = freespin_service.assign_free_spins(game_template.key, "player_12345")
            assert assignment is not None, "Assignment should be created"
            assert assignment.assignment_id == "assignment_12345", "Assignment ID should match"
            print(f"  Free spins assigned: {assignment.assignment_id}")
            
            # Step 5: Check player free spins
            print("  Step 5: Checking player free spins...")
            
            player_spins = freespin_service.get_player_assignments("player_12345")
            assert len(player_spins) >= 0, "Should return player assignments"
            print(f"  Player has {len(player_spins)} free spin assignments")
            
            # Step 6: Cancel free spins
            print("  Step 6: Cancelling free spins...")
            
            cancel_result = freespin_service.cancel_assignment(assignment.assignment_id)
            assert cancel_result is True, "Cancellation should succeed"
            print("  Free spins cancelled successfully")
            
            # Step 7: Test bulk operations
            print("  Step 7: Testing bulk operations...")
            
            player_ids = ["player_1", "player_2", "player_3"]
            bulk_assignments = freespin_service.bulk_assign_free_spins(
                game_template.key, player_ids
            )
            assert len(bulk_assignments) == 3, "Should create 3 assignments"
            print(f"  Bulk assignment completed: {len(bulk_assignments)} assignments")
            
            # Step 8: Get client info
            print("  Step 8: Getting client information...")
            
            client_info = client.get_client_info()
            assert client_info['environment'] == 'local', "Environment should be local"
            assert 'authentication' in client_info, "Should include auth info"
            print("  Client info retrieved successfully")
            
            print("\nComplete workflow integration test PASSED!")
            return True
            
        except Exception as e:
            print(f"\nIntegration test FAILED: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    @patch.dict(os.environ, {'ELANTIL_CLIENT_SECRET': 'test_secret'})
    def test_error_handling(self):
        """Test error handling scenarios"""
        
        print("\nTesting error handling scenarios...")
        
        try:
            # Test with invalid client credentials
            print("  Testing invalid client credentials...")
            
            with patch('requests.Session.post') as mock_post:
                mock_response = Mock()
                mock_response.status_code = 400
                mock_response.json.return_value = {
                    'error': 'invalid_grant',
                    'error_description': 'Refresh token expired'
                }
                mock_response.content = True
                mock_post.return_value = mock_response
                
                try:
                    client = ElantilClient(environment="local")
                    client.test_authentication()
                    assert False, "Should have failed with invalid token"
                except Exception:
                    print("  Invalid credentials properly rejected")
            
            # Test API error handling
            print("  Testing API error handling...")
            
            with patch('requests.Session.post') as mock_post_auth:
                with patch('requests.Session.request') as mock_request:
                    # Mock successful auth
                    mock_auth_response = Mock()
                    mock_auth_response.status_code = 200
                    mock_auth_response.json.return_value = self.mock_responses['auth_success']
                    mock_post_auth.return_value = mock_auth_response
                    
                    # Mock API error
                    mock_api_response = Mock()
                    mock_api_response.status_code = 500
                    mock_api_response.text = "Internal Server Error"
                    mock_request.return_value = mock_api_response
                    
                    client = ElantilClient(environment="local")
                    
                    try:
                        freespin_service = FreeSpinService(client, client.tenant_integration_id)
                        freespin_service.list_templates()
                        assert False, "Should have failed with API error"
                    except Exception:
                        print("  API error properly handled")
            
            print("Error handling tests PASSED!")
            return True
            
        except Exception as e:
            print(f"Error handling test FAILED: {e}")
            return False
    
    @patch.dict(os.environ, {'ELANTIL_CLIENT_SECRET': 'test_secret'})
    def test_configuration_management(self):
        """Test configuration management"""
        
        print("\nTesting configuration management...")
        
        try:
            # Test config loading
            print("  Testing configuration loading...")
            
            config = get_config("local")
            assert config.environment == "local", "Environment should be local"
            
            elantil_config = config.get_elantil_config()
            assert elantil_config['client_id'] == "casino-rewards", "Client ID should be 'casino-rewards'"
            assert elantil_config['tenant_id'] == "monkeytilt", "Tenant ID should be 'monkeytilt'"
            
            print("  Configuration loaded successfully")
            
            # Test config validation
            print("  Testing configuration validation...")
            
            config.validate_elantil_config()
            print("  Configuration validation passed")
            
            # Test freespin config
            print("  Testing freespin configuration...")
            
            freespin_config = config.get_freespin_config()
            assert freespin_config['default_currency'] == "USD", "Default currency should be USD"
            assert freespin_config['default_expiry_hours'] == 24, "Default expiry should be 24 hours"
            
            print("  Freespin configuration correct")
            
            print("Configuration management tests PASSED!")
            return True
            
        except Exception as e:
            print(f"Configuration test FAILED: {e}")
            return False


# Pytest test functions
def test_complete_workflow():
    """Test the complete workflow integration."""
    test_suite = MockElantilIntegrationTest()
    assert test_suite.test_complete_workflow(), "Complete workflow test should pass"


def test_error_handling():
    """Test the error handling integration."""
    test_suite = MockElantilIntegrationTest()
    assert test_suite.test_error_handling(), "Error handling test should pass"


def test_configuration_management():
    """Test the configuration management integration."""
    test_suite = MockElantilIntegrationTest()
    assert test_suite.test_configuration_management(), "Configuration management test should pass"


def run_integration_tests():
    """Run all integration tests"""
    
    print("Starting Casino Rewards Platform Integration Tests")
    print("=" * 60)
    
    test_suite = MockElantilIntegrationTest()
    
    results = {
        'workflow': test_suite.test_complete_workflow(),
        'error_handling': test_suite.test_error_handling(),
        'configuration': test_suite.test_configuration_management()
    }
    
    print("\n" + "=" * 60)
    print("INTEGRATION TEST RESULTS")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    all_passed = all(results.values())
    overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
    
    print(f"\nOverall Result: {overall_status}")
    print("=" * 60)
    
    return all_passed


if __name__ == "__main__":
    success = run_integration_tests()
    exit(0 if success else 1)

