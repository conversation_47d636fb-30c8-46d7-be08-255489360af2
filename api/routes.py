from fastapi import APIRouter, HTTPException
from typing import Dict
from datetime import datetime
from .models import OptimoveWebhookRequest, APIResponse


# Create router instance
router = APIRouter()

# Global variable to store background task results
task_results: Dict[str, Dict] = {}

@router.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Risk Engine API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "active"
    }

@router.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@router.post("/optimove/webhook", response_model=APIResponse)
async def optimove_webhook(request: OptimoveWebhookRequest):
    """
    Optimove webhook endpoint
    
    Receives webhook requests from the Optimove platform for player events.
    """
    try:
        # Simple placeholder implementation
        print("Hello from Optimove webhook!")
        print(f"Received request: {request}")
        
        # Log the webhook data
        print(f"Call ID: {request.call_id}")
        print(f"Player ID: {request.player_id}")
        print(f"Player Name: {request.player_name}")
        print(f"Enable Logging: {request.enable_logging}")
        
        # Return success response
        return APIResponse(
            success=True,
            message="Optimove webhook received successfully",
            data={
                "call_id": request.call_id,
                "player_id": request.player_id,
                "player_name": request.player_name,
                "processed_at": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        print(f"Error processing webhook: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process webhook: {str(e)}"
        )

