"""
FastAPI application for Casino Rewards Platform API
"""

from fastapi import FastAPI
from .routes import router

# Create FastAPI app
app = FastAPI(
    title="Casino Rewards Platform API",
    description="API for managing casino rewards and processing webhooks",
    version="1.0.0"
)

# Include routes
app.include_router(router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)