"""
We will implement the validation logic for API requests and responses here.
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator
import re


class OptimoveWebhookRequest(BaseModel):
    call_id: Optional[str] = Field(None, description="Call ID for the optimove webhook request")
    player_id: Optional[str] = Field(None, description="Player ID for the report")
    player_name: str = Field(..., description="Player name for the report")
    enable_logging: bool = Field(True, description="Enable detailed logging")

    @classmethod
    @field_validator('call_id')
    def validate_call_id(cls, v):
        if v is None:
            return v
        if not re.match(r'^\d{8}$', v):
            raise ValueError('Call ID must be 8 digits')
        return v

    @classmethod
    @field_validator('player_id')
    def validate_player_id(cls, v):
        if v is None:
            return v
        if not re.match(r'^\d{8}$', v):
            raise ValueError('Player ID must be 8 digits')
        return v


class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
